# 2FA Authentication System - Complete Implementation Guide

## Overview

This document provides a comprehensive guide to the Two-Factor Authentication (2FA) system implemented in the Ava Backend using Node.js, Express, JWT tokens, and Google Authenticator (TOTP).

## Features

- **JWT Authentication**: Short-lived access tokens (1h) and long-lived refresh tokens (7d)
- **Two-Factor Authentication**: TOTP-based 2FA using Google Authenticator
- **Backup Codes**: 8 single-use backup codes for account recovery
- **Session Management**: Track and manage user sessions across devices
- **Security Logging**: Comprehensive audit trail for all authentication events
- **Rate Limiting**: Protection against brute force attacks
- **Account Lockout**: Temporary lockout after failed attempts

## Architecture

### Core Components

1. **AuthService** (`src/services/auth/index.ts`)
   - JWT token generation and verification
   - Session management
   - Security event logging
   - Enhanced login flow with 2FA support

2. **TwoFAService** (`src/services/auth/twofa.ts`)
   - TOTP secret generation
   - QR code generation for Google Authenticator
   - Token verification (TOTP and backup codes)
   - 2FA enable/disable functionality

3. **Authentication Middleware** (`src/middleware/auth.ts`)
   - JWT token validation
   - User authentication for protected routes
   - Session activity tracking

4. **Route Handlers**
   - `src/features/auth/routes.ts` - Basic auth endpoints
   - `src/features/auth/twofa-routes.ts` - 2FA management endpoints
   - `src/features/auth/profile-routes.ts` - Protected route examples

## API Endpoints

### Authentication Endpoints

#### POST `/api/auth/register`
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "name": "John Doe",
  "role": "user"
}
```

#### POST `/api/auth/login`
Login user with email and password. Returns tokens if no 2FA, or requires 2FA if enabled.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (No 2FA):**
```json
{
  "success": true,
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "role": "user",
    "name": "John Doe"
  }
}
```

**Response (2FA Required):**
```json
{
  "success": true,
  "require2FA": true,
  "userId": "user-id",
  "message": "Please provide your 2FA token to complete login"
}
```

#### POST `/api/auth/login-2fa`
Complete login with 2FA token verification.

**Request Body:**
```json
{
  "userId": "user-id",
  "token": "123456"
}
```

#### POST `/api/auth/refresh`
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### POST `/api/auth/logout`
Logout user and invalidate refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 2FA Management Endpoints

#### POST `/api/auth/enable-2fa`
Generate 2FA secret and QR code for Google Authenticator setup.

**Headers:** `Authorization: Bearer <accessToken>`

**Response:**
```json
{
  "success": true,
  "data": {
    "qrCodeUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "backupCodes": ["12345678", "87654321", "..."],
    "message": "Scan the QR code with Google Authenticator and verify with a token to enable 2FA"
  }
}
```

#### POST `/api/auth/verify-2fa`
Verify 2FA token and enable 2FA for the user.

**Headers:** `Authorization: Bearer <accessToken>`

**Request Body:**
```json
{
  "token": "123456"
}
```

#### GET `/api/auth/2fa-status`
Check if user has 2FA enabled.

**Headers:** `Authorization: Bearer <accessToken>`

**Response:**
```json
{
  "success": true,
  "data": {
    "enabled": true,
    "verified": true,
    "method": "totp"
  }
}
```

#### POST `/api/auth/disable-2fa`
Disable 2FA after token verification.

**Headers:** `Authorization: Bearer <accessToken>`

**Request Body:**
```json
{
  "token": "123456"
}
```

#### POST `/api/auth/regenerate-backup-codes`
Regenerate backup codes for 2FA.

**Headers:** `Authorization: Bearer <accessToken>`

**Request Body:**
```json
{
  "token": "123456"
}
```

### Protected Route Examples

#### GET `/api/auth/profile`
Get user profile information (requires authentication).

**Headers:** `Authorization: Bearer <accessToken>`

#### PUT `/api/auth/profile`
Update user profile information.

**Headers:** `Authorization: Bearer <accessToken>`

**Request Body:**
```json
{
  "name": "John Doe Updated",
  "phone": "+1234567890",
  "department": "Engineering",
  "preferences": {
    "notifications": true,
    "theme": "dark",
    "language": "en"
  }
}
```

#### GET `/api/auth/sessions`
Get user's active sessions.

#### DELETE `/api/auth/sessions/:sessionId`
Revoke a specific session.

#### POST `/api/auth/revoke-all-sessions`
Revoke all sessions except the current one.

## Implementation Details

### Token Configuration

- **Access Token Expiry**: 1 hour
- **Refresh Token Expiry**: 7 days
- **Session Expiry**: 7 days
- **Algorithm**: HS256

### 2FA Configuration

- **Method**: TOTP (Time-based One-Time Password)
- **Secret Length**: 32 characters (base32)
- **Token Window**: 2 time steps (60 seconds tolerance)
- **Backup Codes**: 8 codes, 8 characters each
- **App Name**: "Ava Chat System"
- **Issuer**: "Ava"

### Security Features

- **Rate Limiting**: Applied to all authentication endpoints
- **Brute Force Protection**: Account lockout after 5 failed attempts
- **Session Tracking**: Monitor active sessions across devices
- **Security Logging**: Audit trail for all authentication events
- **Token Blacklisting**: Revoked tokens are blacklisted
- **Input Validation**: Comprehensive validation for all inputs

## Database Schema

The system uses the following database tables:

- `profiles` - User profile information
- `auth_sessions` - Active user sessions
- `user_2fa` - 2FA configuration and secrets
- `blacklisted_tokens` - Revoked JWT tokens
- `login_attempts` - Login attempt history
- `security_events` - Security audit log

## Setup Instructions

### 1. Install Dependencies

```bash
npm install speakeasy qrcode @types/speakeasy @types/qrcode
```

### 2. Environment Variables

Ensure the following environment variables are set:

```env
JWT_SECRET=your-super-secret-jwt-key
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-supabase-service-key
```

### 3. Database Migration

Run the database migration to create the required tables:

```sql
-- The complete schema is available in migrations/complete_ava_database_schema.sql
```

### 4. Start the Server

```bash
npm run dev
```

## Testing

### Running Tests

```bash
npm test
```

### Test Coverage

The test suite covers:
- 2FA secret generation and QR code creation
- TOTP token verification
- Backup code verification and consumption
- 2FA enable/disable functionality
- Enhanced login flow with 2FA
- Error handling and edge cases

## Postman Collection

Import the Postman collection from `postman/Ava-Backend-2FA-Authentication.postman_collection.json` to test all endpoints.

The collection includes:
- Pre-configured environment variables
- Automatic token extraction and storage
- Complete request examples
- Proper authentication headers

## Security Considerations

1. **Secret Storage**: 2FA secrets are encrypted in the database
2. **Token Validation**: All tokens are validated with proper algorithms
3. **Rate Limiting**: Prevents brute force attacks
4. **Session Management**: Tracks and limits concurrent sessions
5. **Audit Logging**: Comprehensive security event logging
6. **Input Validation**: All inputs are validated and sanitized
7. **Error Handling**: Secure error messages without information leakage

## Usage Flow

### Setting up 2FA

1. User logs in normally
2. User calls `/api/auth/enable-2fa` to get QR code
3. User scans QR code with Google Authenticator
4. User calls `/api/auth/verify-2fa` with token from app
5. 2FA is now enabled for the user

### Login with 2FA

1. User calls `/api/auth/login` with credentials
2. Server returns `require2FA: true` and `userId`
3. User enters token from Google Authenticator
4. User calls `/api/auth/login-2fa` with `userId` and `token`
5. Server returns access and refresh tokens

### Using Backup Codes

If the user loses access to their authenticator app, they can use backup codes by calling `/api/auth/login-2fa` with a backup code instead of a TOTP token.

## Troubleshooting

### Common Issues

1. **Invalid 2FA Token**: Check time synchronization on the device
2. **QR Code Not Scanning**: Ensure the QR code is displayed properly
3. **Token Expired**: Access tokens expire after 1 hour, use refresh token
4. **Account Locked**: Wait 15 minutes or contact administrator

### Debug Logging

Enable debug logging by setting `NODE_ENV=development` to see detailed logs for authentication events.
