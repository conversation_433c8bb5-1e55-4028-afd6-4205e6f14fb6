import { TwoFAService } from '../../services/auth/twofa'
import { AuthService } from '../../services/auth'
import speakeasy from 'speakeasy'

// Mock dependencies
jest.mock('../../lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn(() => ({
      upsert: jest.fn().mockResolvedValue({ error: null }),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: {
                user_id: 'test-user-id',
                method: 'totp',
                secret: 'JBSWY3DPEHPK3PXP',
                backup_codes: ['12345678', '87654321'],
                enabled: true,
                verified: true
              },
              error: null
            }))
          }))
        }))
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          eq: jest.fn().mockResolvedValue({ error: null })
        }))
      })),
      insert: jest.fn().mockResolvedValue({ error: null })
    }))
  }
}))

jest.mock('qrcode', () => ({
  toDataURL: jest.fn().mockResolvedValue('data:image/png;base64,mockqrcode')
}))

describe('TwoFAService', () => {
  const mockUserId = 'test-user-id'
  const mockUserEmail = '<EMAIL>'
  const mockSecret = 'JBSWY3DPEHPK3PXP'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('generateSecret', () => {
    it('should generate a 2FA secret and QR code', async () => {
      const result = await TwoFAService.generateSecret(mockUserId, mockUserEmail)

      expect(result).toHaveProperty('secret')
      expect(result).toHaveProperty('qrCodeUrl')
      expect(result).toHaveProperty('backupCodes')
      expect(result.qrCodeUrl).toBe('data:image/png;base64,mockqrcode')
      expect(result.backupCodes).toHaveLength(8)
      expect(typeof result.secret).toBe('string')
    })

    it('should store the secret in the database', async () => {
      const { supabaseAdmin } = require('../../lib/supabase')
      
      await TwoFAService.generateSecret(mockUserId, mockUserEmail)

      expect(supabaseAdmin.from).toHaveBeenCalledWith('user_2fa')
      expect(supabaseAdmin.from().upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: mockUserId,
          method: 'totp',
          enabled: false,
          verified: false
        }),
        { onConflict: 'user_id,method' }
      )
    })
  })

  describe('verifyToken', () => {
    it('should verify a valid TOTP token', async () => {
      // Generate a valid token for testing
      const token = speakeasy.totp({
        secret: mockSecret,
        encoding: 'base32'
      })

      const result = await TwoFAService.verifyToken(mockUserId, token)
      expect(result).toBe(true)
    })

    it('should reject an invalid TOTP token', async () => {
      const result = await TwoFAService.verifyToken(mockUserId, '000000')
      expect(result).toBe(false)
    })

    it('should verify a valid backup code', async () => {
      const result = await TwoFAService.verifyToken(mockUserId, '12345678', true)
      expect(result).toBe(true)
    })

    it('should reject an invalid backup code', async () => {
      const result = await TwoFAService.verifyToken(mockUserId, 'invalid', true)
      expect(result).toBe(false)
    })
  })

  describe('enable2FA', () => {
    it('should enable 2FA after successful token verification', async () => {
      const token = speakeasy.totp({
        secret: mockSecret,
        encoding: 'base32'
      })

      const result = await TwoFAService.enable2FA(mockUserId, token)
      expect(result).toBe(true)
    })

    it('should not enable 2FA with invalid token', async () => {
      const result = await TwoFAService.enable2FA(mockUserId, '000000')
      expect(result).toBe(false)
    })
  })

  describe('disable2FA', () => {
    it('should disable 2FA after successful token verification', async () => {
      const token = speakeasy.totp({
        secret: mockSecret,
        encoding: 'base32'
      })

      const result = await TwoFAService.disable2FA(mockUserId, token)
      expect(result).toBe(true)
    })

    it('should not disable 2FA with invalid token', async () => {
      const result = await TwoFAService.disable2FA(mockUserId, '000000')
      expect(result).toBe(false)
    })
  })

  describe('is2FAEnabled', () => {
    it('should return true when 2FA is enabled', async () => {
      const result = await TwoFAService.is2FAEnabled(mockUserId)
      expect(result).toBe(true)
    })

    it('should return false when 2FA is not enabled', async () => {
      const { supabaseAdmin } = require('../../lib/supabase')
      supabaseAdmin.from().select().eq().eq().eq().single.mockResolvedValueOnce({
        data: null,
        error: { message: 'No rows found' }
      })

      const result = await TwoFAService.is2FAEnabled(mockUserId)
      expect(result).toBe(false)
    })
  })

  describe('get2FAConfig', () => {
    it('should return 2FA configuration for user', async () => {
      const result = await TwoFAService.get2FAConfig(mockUserId)

      expect(result).toEqual({
        userId: mockUserId,
        method: 'totp',
        secret: mockSecret,
        enabled: true,
        verified: true,
        backupCodes: ['12345678', '87654321']
      })
    })

    it('should return null when no 2FA config exists', async () => {
      const { supabaseAdmin } = require('../../lib/supabase')
      supabaseAdmin.from().select().eq().eq().single.mockResolvedValueOnce({
        data: null,
        error: { message: 'No rows found' }
      })

      const result = await TwoFAService.get2FAConfig(mockUserId)
      expect(result).toBe(null)
    })
  })

  describe('regenerateBackupCodes', () => {
    it('should regenerate backup codes after token verification', async () => {
      const token = speakeasy.totp({
        secret: mockSecret,
        encoding: 'base32'
      })

      const result = await TwoFAService.regenerateBackupCodes(mockUserId, token)
      
      expect(result).toBeInstanceOf(Array)
      expect(result).toHaveLength(8)
      expect(result![0]).toMatch(/^[0-9A-F]{8}$/)
    })

    it('should not regenerate backup codes with invalid token', async () => {
      const result = await TwoFAService.regenerateBackupCodes(mockUserId, '000000')
      expect(result).toBe(null)
    })
  })
})

describe('AuthService 2FA Integration', () => {
  const mockEmail = '<EMAIL>'
  const mockPassword = 'password123'
  const mockUserId = 'test-user-id'
  const mockIpAddress = '127.0.0.1'
  const mockUserAgent = 'Test Agent'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('loginWithCredentials', () => {
    it('should return require2FA flag when user has 2FA enabled', async () => {
      // Mock successful Supabase authentication
      const mockSupabaseAuth = {
        signInWithPassword: jest.fn().mockResolvedValue({
          data: { user: { id: mockUserId, email: mockEmail } },
          error: null
        })
      }

      // Mock profile fetch
      const mockSupabaseFrom = {
        from: jest.fn(() => ({
          select: jest.fn(() => ({
            eq: jest.fn(() => ({
              single: jest.fn().mockResolvedValue({
                data: { id: mockUserId, role: 'user', name: 'Test User' },
                error: null
              })
            }))
          }))
        }))
      }

      // Mock 2FA enabled
      jest.spyOn(TwoFAService, 'is2FAEnabled').mockResolvedValue(true)
      jest.spyOn(AuthService, 'logLoginAttempt').mockResolvedValue()

      const result = await AuthService.loginWithCredentials(
        mockEmail,
        mockPassword,
        mockIpAddress,
        mockUserAgent
      )

      expect(result.success).toBe(true)
      expect(result.require2FA).toBe(true)
      expect(result.tokens).toBeUndefined()
    })

    it('should return tokens when user does not have 2FA enabled', async () => {
      // Mock 2FA disabled
      jest.spyOn(TwoFAService, 'is2FAEnabled').mockResolvedValue(false)
      jest.spyOn(AuthService, 'createSession').mockResolvedValue('session-id')
      jest.spyOn(AuthService, 'generateTokens').mockReturnValue({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        jwtId: 'jwt-id'
      })
      jest.spyOn(AuthService, 'logLoginAttempt').mockResolvedValue()
      jest.spyOn(AuthService, 'logSecurityEvent').mockResolvedValue()

      const result = await AuthService.loginWithCredentials(
        mockEmail,
        mockPassword,
        mockIpAddress,
        mockUserAgent
      )

      expect(result.success).toBe(true)
      expect(result.require2FA).toBeUndefined()
      expect(result.tokens).toBeDefined()
      expect(result.tokens!.accessToken).toBe('access-token')
    })
  })

  describe('complete2FALogin', () => {
    it('should return tokens after successful 2FA verification', async () => {
      jest.spyOn(TwoFAService, 'verifyToken').mockResolvedValue(true)
      jest.spyOn(AuthService, 'createSession').mockResolvedValue('session-id')
      jest.spyOn(AuthService, 'generateTokens').mockReturnValue({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        jwtId: 'jwt-id'
      })
      jest.spyOn(AuthService, 'logSecurityEvent').mockResolvedValue()

      const result = await AuthService.complete2FALogin(
        mockUserId,
        '123456',
        mockIpAddress,
        mockUserAgent
      )

      expect(result.success).toBe(true)
      expect(result.tokens).toBeDefined()
      expect(result.tokens!.accessToken).toBe('access-token')
    })

    it('should fail with invalid 2FA token', async () => {
      jest.spyOn(TwoFAService, 'verifyToken').mockResolvedValue(false)
      jest.spyOn(AuthService, 'logSecurityEvent').mockResolvedValue()

      const result = await AuthService.complete2FALogin(
        mockUserId,
        '000000',
        mockIpAddress,
        mockUserAgent
      )

      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid 2FA token')
      expect(result.tokens).toBeUndefined()
    })
  })
})
