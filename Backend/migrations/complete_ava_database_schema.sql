-- =====================================================
-- COMPLETE AVA CHAT SYSTEM DATABASE SCHEMA
-- All Tables, RLS Policies, Indexes, Functions & Triggers
-- Production-Ready with Optimal Performance
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- STEP 1: DROP EXISTING TABLES (Clean Setup)
-- =====================================================
DROP TABLE IF EXISTS support_requests CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXISTS user_2fa CASCADE;
DROP TABLE IF EXISTS password_history CASCADE;
DROP TABLE IF EXISTS account_lockouts CASCADE;
DROP TABLE IF EXISTS security_events CASCADE;
DROP TABLE IF EXISTS login_attempts CASCADE;
DROP TABLE IF EXISTS blacklisted_tokens CASCADE;
DROP TABLE IF EXISTS auth_sessions CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- =====================================================
-- STEP 2: CREATE ALL TABLES
-- =====================================================

-- 1. PROFILES TABLE (Enhanced User Management)
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 100),
    role TEXT NOT NULL CHECK (role IN ('user', 'support', 'admin')) DEFAULT 'user',
    avatar_url TEXT,
    phone TEXT,
    department TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    preferences JSONB DEFAULT '{
        "notifications": true,
        "theme": "light",
        "language": "en"
    }'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 2. AUTH SESSIONS TABLE (Session Management)
CREATE TABLE auth_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_info TEXT NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT NOT NULL,
    location TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
    is_active BOOLEAN DEFAULT TRUE,
    session_data JSONB DEFAULT '{}'::jsonb
);

-- 3. BLACKLISTED TOKENS TABLE (Token Revocation)
CREATE TABLE blacklisted_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    jti TEXT UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token_type TEXT NOT NULL CHECK (token_type IN ('access', 'refresh')) DEFAULT 'access',
    expires_at TIMESTAMPTZ NOT NULL,
    reason TEXT NOT NULL CHECK (reason IN ('logout', 'security', 'expired', 'revoked')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 4. LOGIN ATTEMPTS TABLE (Brute Force Protection)
CREATE TABLE login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    attempt_count INTEGER DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 5. SECURITY EVENTS TABLE (Audit Logging)
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    event_category TEXT NOT NULL CHECK (event_category IN ('auth', 'session', 'profile', 'security', 'system')),
    ip_address INET NOT NULL,
    user_agent TEXT NOT NULL,
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    severity TEXT DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. ACCOUNT LOCKOUTS TABLE (Account Security)
CREATE TABLE account_lockouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL,
    ip_address INET NOT NULL,
    lockout_type TEXT NOT NULL CHECK (lockout_type IN ('brute_force', 'security', 'admin')),
    locked_until TIMESTAMPTZ NOT NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    released_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 7. PASSWORD HISTORY TABLE (Password Reuse Prevention)
CREATE TABLE password_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. USER 2FA TABLE (Two-Factor Authentication)
CREATE TABLE user_2fa (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    method TEXT NOT NULL CHECK (method IN ('totp', 'sms', 'email')),
    secret TEXT NOT NULL,
    backup_codes TEXT[],
    enabled BOOLEAN DEFAULT FALSE,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ
);

-- 9. CONVERSATIONS TABLE (Chat System)
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    support_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    title TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'assigned', 'in_progress', 'resolved', 'closed')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    category TEXT DEFAULT 'general' CHECK (category IN ('general', 'technical', 'billing', 'feature_request', 'bug_report')),
    jira_ticket_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    closed_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 10. MESSAGES TABLE (Chat Messages)
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    message_type TEXT NOT NULL CHECK (message_type IN ('text', 'voice', 'file', 'system')),
    content TEXT NOT NULL,
    voice_url TEXT,
    file_url TEXT,
    file_name TEXT,
    file_size INTEGER,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 11. SUPPORT REQUESTS TABLE (Support Ticket Management)
CREATE TABLE support_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    jira_ticket_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_customer', 'resolved', 'closed')),
    assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    resolution TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- =====================================================
-- STEP 3: CREATE PERFORMANCE INDEXES
-- =====================================================

-- Profiles indexes
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_active ON profiles(is_active);
CREATE INDEX idx_profiles_last_login ON profiles(last_login_at);
CREATE INDEX idx_profiles_department ON profiles(department);

-- Auth sessions indexes
CREATE INDEX idx_auth_sessions_user_id ON auth_sessions(user_id);
CREATE INDEX idx_auth_sessions_active ON auth_sessions(is_active);
CREATE INDEX idx_auth_sessions_expires ON auth_sessions(expires_at);
CREATE INDEX idx_auth_sessions_last_activity ON auth_sessions(last_activity);
CREATE INDEX idx_auth_sessions_ip ON auth_sessions(ip_address);

-- Blacklisted tokens indexes
CREATE UNIQUE INDEX idx_blacklisted_tokens_jti ON blacklisted_tokens(jti);
CREATE INDEX idx_blacklisted_tokens_user_id ON blacklisted_tokens(user_id);
CREATE INDEX idx_blacklisted_tokens_expires ON blacklisted_tokens(expires_at);
CREATE INDEX idx_blacklisted_tokens_type ON blacklisted_tokens(token_type);

-- Login attempts indexes
CREATE INDEX idx_login_attempts_email ON login_attempts(email);
CREATE INDEX idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX idx_login_attempts_created ON login_attempts(created_at);
CREATE INDEX idx_login_attempts_success ON login_attempts(success);

-- Security events indexes
CREATE INDEX idx_security_events_user_id ON security_events(user_id);
CREATE INDEX idx_security_events_type ON security_events(event_type);
CREATE INDEX idx_security_events_category ON security_events(event_category);
CREATE INDEX idx_security_events_severity ON security_events(severity);
CREATE INDEX idx_security_events_created ON security_events(created_at);
CREATE INDEX idx_security_events_ip ON security_events(ip_address);

-- Account lockouts indexes
CREATE INDEX idx_account_lockouts_email ON account_lockouts(email);
CREATE INDEX idx_account_lockouts_ip ON account_lockouts(ip_address);
CREATE INDEX idx_account_lockouts_until ON account_lockouts(locked_until);
CREATE INDEX idx_account_lockouts_type ON account_lockouts(lockout_type);

-- Password history indexes
CREATE INDEX idx_password_history_user_id ON password_history(user_id);
CREATE INDEX idx_password_history_created ON password_history(created_at);

-- 2FA indexes
CREATE INDEX idx_user_2fa_user_id ON user_2fa(user_id);
CREATE INDEX idx_user_2fa_method ON user_2fa(method);
CREATE INDEX idx_user_2fa_enabled ON user_2fa(enabled);

-- Conversations indexes
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_support_id ON conversations(support_id);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_priority ON conversations(priority);
CREATE INDEX idx_conversations_category ON conversations(category);
CREATE INDEX idx_conversations_created ON conversations(created_at);
CREATE INDEX idx_conversations_updated ON conversations(updated_at);
CREATE INDEX idx_conversations_jira ON conversations(jira_ticket_id);

-- Messages indexes
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_type ON messages(message_type);
CREATE INDEX idx_messages_created ON messages(created_at);

-- Support requests indexes
CREATE INDEX idx_support_requests_conversation_id ON support_requests(conversation_id);
CREATE INDEX idx_support_requests_jira ON support_requests(jira_ticket_id);
CREATE INDEX idx_support_requests_status ON support_requests(status);
CREATE INDEX idx_support_requests_priority ON support_requests(priority);
CREATE INDEX idx_support_requests_assigned ON support_requests(assigned_to);
CREATE INDEX idx_support_requests_created ON support_requests(created_at);

-- Composite indexes for common queries
CREATE INDEX idx_conversations_user_status ON conversations(user_id, status);
CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at);
CREATE INDEX idx_auth_sessions_user_active ON auth_sessions(user_id, is_active);
CREATE INDEX idx_login_attempts_email_created ON login_attempts(email, created_at);
CREATE INDEX idx_security_events_user_created ON security_events(user_id, created_at);

-- =====================================================
-- STEP 4: ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE blacklisted_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_lockouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_2fa ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_requests ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 5: CREATE RLS POLICIES (Non-Recursive)
-- =====================================================

-- PROFILES POLICIES
CREATE POLICY "profiles_select_own" ON profiles
    FOR SELECT USING (auth.uid() = id);
CREATE POLICY "profiles_update_own" ON profiles
    FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "profiles_service_all" ON profiles
    FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "profiles_support_view" ON profiles
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM auth.users
            WHERE id IN (
                SELECT user_id FROM profiles WHERE role = 'support'
            )
        )
    );

-- AUTH SESSIONS POLICIES
CREATE POLICY "sessions_select_own" ON auth_sessions
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "sessions_insert_own" ON auth_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "sessions_update_own" ON auth_sessions
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "sessions_delete_own" ON auth_sessions
    FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "sessions_service_all" ON auth_sessions
    FOR ALL USING (auth.role() = 'service_role');

-- BLACKLISTED TOKENS POLICIES
CREATE POLICY "tokens_select_own" ON blacklisted_tokens
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "tokens_insert_own" ON blacklisted_tokens
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "tokens_service_all" ON blacklisted_tokens
    FOR ALL USING (auth.role() = 'service_role');

-- LOGIN ATTEMPTS POLICIES (Service role only for security)
CREATE POLICY "login_attempts_service_all" ON login_attempts
    FOR ALL USING (auth.role() = 'service_role');

-- SECURITY EVENTS POLICIES
CREATE POLICY "security_events_select_own" ON security_events
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "security_events_service_all" ON security_events
    FOR ALL USING (auth.role() = 'service_role');

-- ACCOUNT LOCKOUTS POLICIES (Service role only)
CREATE POLICY "lockouts_service_all" ON account_lockouts
    FOR ALL USING (auth.role() = 'service_role');

-- PASSWORD HISTORY POLICIES (Service role only)
CREATE POLICY "password_history_service_all" ON password_history
    FOR ALL USING (auth.role() = 'service_role');

-- USER 2FA POLICIES
CREATE POLICY "user_2fa_select_own" ON user_2fa
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "user_2fa_insert_own" ON user_2fa
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "user_2fa_update_own" ON user_2fa
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "user_2fa_delete_own" ON user_2fa
    FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "user_2fa_service_all" ON user_2fa
    FOR ALL USING (auth.role() = 'service_role');

-- CONVERSATIONS POLICIES
CREATE POLICY "conversations_select_participant" ON conversations
    FOR SELECT USING (
        auth.uid() = user_id OR
        auth.uid() = support_id OR
        auth.role() = 'service_role'
    );
CREATE POLICY "conversations_insert_own" ON conversations
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "conversations_update_participant" ON conversations
    FOR UPDATE USING (
        auth.uid() = user_id OR
        auth.uid() = support_id OR
        auth.role() = 'service_role'
    );
CREATE POLICY "conversations_service_all" ON conversations
    FOR ALL USING (auth.role() = 'service_role');

-- MESSAGES POLICIES
CREATE POLICY "messages_select_conversation_participant" ON messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM conversations
            WHERE conversations.id = messages.conversation_id
            AND (
                conversations.user_id = auth.uid() OR
                conversations.support_id = auth.uid()
            )
        ) OR auth.role() = 'service_role'
    );
CREATE POLICY "messages_insert_conversation_participant" ON messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM conversations
            WHERE conversations.id = messages.conversation_id
            AND (
                conversations.user_id = auth.uid() OR
                conversations.support_id = auth.uid()
            )
        ) OR auth.role() = 'service_role'
    );
CREATE POLICY "messages_update_own" ON messages
    FOR UPDATE USING (auth.uid() = sender_id OR auth.role() = 'service_role');
CREATE POLICY "messages_service_all" ON messages
    FOR ALL USING (auth.role() = 'service_role');

-- SUPPORT REQUESTS POLICIES
CREATE POLICY "support_requests_select_support" ON support_requests
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM auth.users
            WHERE id IN (
                SELECT user_id FROM profiles WHERE role IN ('support', 'admin')
            )
        ) OR auth.role() = 'service_role'
    );
CREATE POLICY "support_requests_service_all" ON support_requests
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- STEP 6: CREATE UTILITY FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to clean expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM auth_sessions
    WHERE expires_at < NOW() OR last_activity < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM blacklisted_tokens WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old login attempts
CREATE OR REPLACE FUNCTION cleanup_old_login_attempts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM login_attempts WHERE created_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old security events
CREATE OR REPLACE FUNCTION cleanup_old_security_events()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM security_events WHERE created_at < NOW() - INTERVAL '90 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to check if account is locked
CREATE OR REPLACE FUNCTION is_account_locked(user_email TEXT, user_ip INET)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM account_lockouts
        WHERE (email = user_email OR ip_address = user_ip)
        AND locked_until > NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 7: CREATE TRIGGERS
-- =====================================================

-- Trigger to update updated_at on profiles
CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update updated_at on conversations
CREATE TRIGGER trigger_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update updated_at on support_requests
CREATE TRIGGER trigger_support_requests_updated_at
    BEFORE UPDATE ON support_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update updated_at on user_2fa
CREATE TRIGGER trigger_user_2fa_updated_at
    BEFORE UPDATE ON user_2fa
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STEP 8: GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON auth_sessions TO authenticated;
GRANT SELECT, INSERT ON blacklisted_tokens TO authenticated;
GRANT SELECT ON security_events TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_2fa TO authenticated;
GRANT SELECT, INSERT, UPDATE ON conversations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON messages TO authenticated;

-- Grant full permissions to service role
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- STEP 9: CREATE SAMPLE DATA (Optional Test Users)
-- =====================================================

-- Note: These will be created via the API, but here's the structure
-- Test users will be created with:
-- 1. <EMAIL> (regular user)
-- 2. <EMAIL> (support agent)
-- 3. <EMAIL> (admin user)

-- =====================================================
-- STEP 10: PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Set table storage parameters for better performance
ALTER TABLE profiles SET (fillfactor = 90);
ALTER TABLE auth_sessions SET (fillfactor = 80);
ALTER TABLE messages SET (fillfactor = 85);
ALTER TABLE conversations SET (fillfactor = 90);

-- Create partial indexes for active records
CREATE INDEX CONCURRENTLY idx_active_sessions
    ON auth_sessions(user_id, last_activity)
    WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_open_conversations
    ON conversations(user_id, created_at)
    WHERE status IN ('open', 'assigned');

CREATE INDEX CONCURRENTLY idx_recent_messages
    ON messages(conversation_id, created_at DESC)
    WHERE created_at > NOW() - INTERVAL '30 days';

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT
    'AVA CHAT SYSTEM DATABASE SETUP COMPLETED SUCCESSFULLY!' as status,
    'All tables, indexes, RLS policies, functions, and triggers are ready.' as message,
    'You can now start the application and test all endpoints.' as next_step;
