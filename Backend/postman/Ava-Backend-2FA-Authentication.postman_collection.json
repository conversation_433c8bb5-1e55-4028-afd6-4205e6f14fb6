{"info": {"name": "Ava Backend - 2FA Authentication System", "description": "Complete API collection for Ava Backend with JWT authentication and 2FA support using Google Authenticator (TOTP)", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "sessionId", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"name\": \"<PERSON>\",\n  \"role\": \"user\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user account"}, "response": []}, {"name": "<PERSON><PERSON> (No 2FA)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.collectionVariables.set('accessToken', response.accessToken);", "        pm.collectionVariables.set('refreshToken', response.refreshToken);", "        pm.collectionVariables.set('userId', response.user.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login user (returns tokens if no 2FA, or require2FA flag if 2FA is enabled)"}, "response": []}, {"name": "Login (With 2FA Required)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.require2FA && response.userId) {", "        pm.collectionVariables.set('userId', response.userId);", "        console.log('2FA required. Use login-2fa endpoint with TOTP token.');", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login for user with 2FA enabled (returns require2FA: true)"}, "response": []}, {"name": "Complete 2FA Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.collectionVariables.set('accessToken', response.data.accessToken);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "        pm.collectionVariables.set('sessionId', response.data.sessionId);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{userId}}\",\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login-2fa", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login-2fa"]}, "description": "Complete login with 2FA token from Google Authenticator"}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.collectionVariables.set('accessToken', response.accessToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}, "description": "Refresh access token using refresh token"}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}, "description": "Logout user and invalidate refresh token"}, "response": []}], "description": "Basic authentication endpoints"}, {"name": "Two-Factor Authentication (2FA)", "item": [{"name": "Enable 2FA - Generate QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/auth/enable-2fa", "host": ["{{baseUrl}}"], "path": ["api", "auth", "enable-2fa"]}, "description": "Generate 2FA secret and QR code. Scan the QR code with Google Authenticator app."}, "response": []}, {"name": "Verify 2FA - Complete Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-2fa", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-2fa"]}, "description": "Verify 2FA token from Google Authenticator to enable 2FA"}, "response": []}, {"name": "Check 2FA Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/2fa-status", "host": ["{{baseUrl}}"], "path": ["api", "auth", "2fa-status"]}, "description": "Check if user has 2FA enabled"}, "response": []}, {"name": "Disable 2FA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/disable-2fa", "host": ["{{baseUrl}}"], "path": ["api", "auth", "disable-2fa"]}, "description": "Disable 2FA after verifying current token"}, "response": []}, {"name": "Regenerate Backup Codes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/regenerate-backup-codes", "host": ["{{baseUrl}}"], "path": ["api", "auth", "regenerate-backup-codes"]}, "description": "Regenerate backup codes for 2FA"}, "response": []}], "description": "Two-factor authentication management endpoints"}, {"name": "Protected Routes", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}, "description": "Get user profile information (protected route example)"}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"+1234567890\",\n  \"department\": \"Engineering\",\n  \"preferences\": {\n    \"notifications\": true,\n    \"theme\": \"dark\",\n    \"language\": \"en\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}, "description": "Update user profile information"}, "response": []}, {"name": "Get Active Sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/sessions", "host": ["{{baseUrl}}"], "path": ["api", "auth", "sessions"]}, "description": "Get user's active sessions"}, "response": []}, {"name": "Revoke Session", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/sessions/:sessionId", "host": ["{{baseUrl}}"], "path": ["api", "auth", "sessions", ":sessionId"], "variable": [{"key": "sessionId", "value": "session-id-here", "description": "Session ID to revoke"}]}, "description": "Revoke a specific session"}, "response": []}, {"name": "Revoke All Other Sessions", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/auth/revoke-all-sessions", "host": ["{{baseUrl}}"], "path": ["api", "auth", "revoke-all-sessions"]}, "description": "Revoke all sessions except the current one"}, "response": []}], "description": "Protected routes that require valid JWT authentication"}, {"name": "Health Check", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check if the server is running"}, "response": []}], "description": "Server health check endpoint"}]}